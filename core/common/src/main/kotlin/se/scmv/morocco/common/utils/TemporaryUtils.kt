package se.scmv.morocco.common.utils

import android.content.Context
import android.content.Intent

/**
 * Utility object containing temporary utility functions that will be removed once all the app is in compose.
 */
object TemporaryUtils {

    /**
     * Restarts the application by relaunching the main activity.
     *
     * This function terminates the current process and starts the main activity of the app as if the app was
     * launched from scratch. It can be used in scenarios where a complete application restart is required,
     * such as after a shop account is logged in so that the [FlavorUtils] preferences will be reinitialized.
     *
     * @param ctx The application context, required to access package and activity information.
     * Note: This method forcibly exits the app and restarts it. Use with caution as it may interrupt user workflows.
     */
    fun rebirthApp(ctx: Context) {
        val packageManager = ctx.packageManager
        val intent = packageManager.getLaunchIntentForPackage(ctx.packageName)
        val componentName = intent!!.component
        val mainIntent = Intent.makeRestartActivityTask(componentName)
        ctx.startActivity(mainIntent)
        Runtime.getRuntime().exit(0)
    }
}