package se.scmv.morocco.utils

import android.util.Log
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import se.scmv.morocco.BuildConfig

/**
 * Centralized configuration for the app
 * Contains all configuration values that should not be hardcoded
 */
object AppConfig {
    /**
     * Application ID - same as BuildConfig.APPLICATION_ID
     */
    const val APPLICATION_ID: String = BuildConfig.APPLICATION_ID
    
    /**
     * TikTok App ID for TikTok Business SDK
     */
    const val TIKTOK_APP_ID: String = BuildConfig.TIKTOK_APP_ID
    
    /**
     * Whether the app is in developer mode
     */
    val IS_DEVELOPER_MODE: Boolean = BuildConfig.IS_DEVELOPER_MODE
    
    /**
     * App version name
     */
    const val VERSION_NAME: String = BuildConfig.VERSION_NAME
    
    /**
     * App version code
     */
    const val VERSION_CODE: Int = BuildConfig.VERSION_CODE

    //TikTok Remote Config - Supports multiple App IDs (comma-separated format for SDK >= 1.3.1)
    const val TIKTOK_APP_IDS_LIST: String = "tiktok_app_ids_list"
    
    /**
     * Get TikTok App IDs from remote config
     * Returns comma-separated app IDs from the list, or falls back to BuildConfig value
     */
    fun getTikTokAppIdsFromRemoteConfig(): String {
        return try {
            val remoteConfig = FirebaseRemoteConfig.getInstance()
            val appIdsJson = remoteConfig.getString(TIKTOK_APP_IDS_LIST)
            
            // Debug logging to see what we're getting
            android.util.Log.d("TikTokConfig", "Raw JSON from remote config: '$appIdsJson'")
            android.util.Log.d("TikTokConfig", "JSON length: ${appIdsJson.length}")
            
            val gson = Gson()
            val type = object : TypeToken<List<String>>() {}.type
            val appIds: List<String> = gson.fromJson(appIdsJson, type)
            
            // Debug logging to see what was parsed
            Log.d("TikTokConfig", "Parsed app IDs: $appIds")
            Log.d("TikTokConfig", "App IDs count: ${appIds.size}")
            appIds.forEachIndexed { index, id ->
                Log.d("TikTokConfig", "App ID $index: '$id'")
            }
            
            if (appIds.isNotEmpty()) {
                val result = appIds.joinToString(",")
                Log.d("TikTokConfig", "Final comma-separated result: '$result'")
                result // Return comma-separated app IDs
            } else {
                Log.d("TikTokConfig", "Empty list, using fallback: $TIKTOK_APP_ID")
                TIKTOK_APP_ID // Fallback to BuildConfig value
            }
        } catch (e: Exception) {
            Log.e("TikTokConfig", "Error parsing TikTok App IDs: ${e.message}")
            TIKTOK_APP_ID // Fallback to BuildConfig value on any error
        }
    }
    
    /**
     * Force refresh remote config (useful for testing)
     * Call this method to force fetch fresh values from Firebase
     */
    fun forceRefreshRemoteConfig() {
        try {
            val remoteConfig = FirebaseRemoteConfig.getInstance()
            // Force fetch with 0 cache expiration
            remoteConfig.fetch(0).addOnSuccessListener {
                remoteConfig.activate()
                android.util.Log.d("TikTokConfig", "Remote config force refreshed successfully")
            }.addOnFailureListener { e ->
                android.util.Log.e("TikTokConfig", "Failed to force refresh remote config: ${e.message}", e)
            }
        } catch (e: Exception) {
            android.util.Log.e("TikTokConfig", "Failed to force refresh remote config: ${e.message}", e)
        }
    }
} 