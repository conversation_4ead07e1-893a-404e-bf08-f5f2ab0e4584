package se.scmv.morocco.avitov2.core.domain.usecases

import android.content.Context
import android.util.Log
import com.braze.Braze
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.qualifiers.ApplicationContext
import se.scmv.morocco.authentication.presentation.common.LoginType
import se.scmv.morocco.services.TAG
import javax.inject.Inject

class CreateLoginSessionUseCase @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseMessaging: FirebaseMessaging
) {
    suspend operator fun invoke(loginType: LoginType, token: String) {
        createSessionAndSignInMessaging(token)
    }

    private fun createSessionAndSignInMessaging(accessToken: String) {
        // register new token to firebase messaging service
        runCatching {
            firebaseMessaging.token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    android.util.Log.w(
                        TAG,
                        "Fetching FCM registration token failed",
                        task.exception
                    )
                    return@addOnCompleteListener
                }
                Braze.getInstance(context).registeredPushToken = task.result
            }
        }.onFailure {
            Log.e(
                "TEST",
                "Exception while registering Firebase token with Braze.",
                it
            )
        }
    }
}