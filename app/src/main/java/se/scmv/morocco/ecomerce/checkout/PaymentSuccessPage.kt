package se.scmv.morocco.ecomerce.checkout

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import se.scmv.morocco.R
import se.scmv.morocco.account.presentation.orders.AccountOrdersActivity
import se.scmv.morocco.databinding.OrderSuccessFragmentBinding

const val CC_PAYMENT = "CC_PAYMENT"

class PaymentSuccessPage : AppCompatActivity() {

    private var _binding: OrderSuccessFragmentBinding? = null
    private val binding: OrderSuccessFragmentBinding
        get() = _binding!!

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = OrderSuccessFragmentBinding.inflate(layoutInflater)
        setContentView(_binding?.root)

        with(binding) {
            pitch.text = if (intent.getBooleanExtra(CC_PAYMENT, false)) {
                getString(R.string.order_thank_you_CC)
            } else {
                getString(R.string.order_thank_you_COD)
            }
            gotoBuy.setOnClickListener {
                val ecommerceIntent = Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("scm://app.avito/listing?category=5010&dlvr=1")
                )
                startActivity(ecommerceIntent)
            }
            gotoMyOrders.setOnClickListener {
                val intent = Intent(this@PaymentSuccessPage, AccountOrdersActivity::class.java)
                startActivity(intent)
                finish()
            }
        }
    }
}