<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="se.scmv.morocco.avitov2.vas.presentation.activities.ReceiptActivity">
    <!--Toolbar-->
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_above="@+id/receipt_footer">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <ImageView
                    android:layout_width="@dimen/icon_size_small"
                    android:layout_height="@dimen/icon_size_small"
                    android:layout_margin="@dimen/space_normal"
                    android:src="@drawable/ic_approve_email_sent"
                    android:tint="@color/approve_icon_color" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_sent_email_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/space_normal"
                    android:lineSpacingExtra="@dimen/text_size_tiny"
                    android:text="@string/vas_receipt_email_sent_message"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/text_size_normal"
                    android:textStyle="bold" />
            </LinearLayout>

            <RelativeLayout
                android:id="@+id/receipt_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/vas_receipt_table_container"
                android:padding="@dimen/space_big">

                <ImageView
                    android:layout_width="@dimen/icon_size_normal"
                    android:layout_height="@dimen/icon_size_normal"
                    android:layout_alignParentEnd="true"
                    android:layout_alignParentTop="true"
                    android:src="@drawable/avito_logo"
                    android:layout_alignParentRight="true" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/your_receipt_receipt"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/text_size_big"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_date_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_title"
                    android:layout_marginTop="@dimen/space_normal"
                    android:text="@string/date_receipt"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/space_normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_title"
                    android:layout_marginStart="@dimen/space_normal"
                    android:layout_marginTop="@dimen/space_normal"
                    android:layout_toEndOf="@+id/receipt_date_label"
                    android:textSize="@dimen/space_normal"
                    tools:text="2016-05-30"
                    android:layout_marginLeft="@dimen/space_normal"
                    android:layout_toRightOf="@+id/receipt_date_label" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_name_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_date_label"
                    android:layout_marginTop="@dimen/space_normal"
                    android:text="@string/receipt_for"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/space_normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_receiver_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_date_label"
                    android:layout_marginStart="@dimen/space_normal"
                    android:layout_marginTop="@dimen/space_normal"
                    android:layout_toEndOf="@+id/receipt_name_label"
                    android:textSize="@dimen/space_normal"
                    tools:text="Bachiri Taoufiq abderrahman"
                    android:layout_marginLeft="@dimen/space_normal"
                    android:layout_toRightOf="@+id/receipt_name_label" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_transaction_number_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_name_label"
                    android:layout_marginTop="@dimen/space_normal"
                    android:text="@string/transaction_number_receipt"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/space_normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_transaction_number"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_name_label"
                    android:layout_marginStart="@dimen/space_normal"
                    android:layout_marginTop="@dimen/space_normal"
                    android:layout_toEndOf="@+id/receipt_transaction_number_label"
                    android:textSize="@dimen/space_normal"
                    tools:text="837421"
                    android:layout_marginLeft="@dimen/space_normal"
                    android:layout_toRightOf="@+id/receipt_transaction_number_label" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_payment_method_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_transaction_number_label"
                    android:layout_marginTop="@dimen/space_normal"
                    android:text="@string/payment_method_receipt"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/space_normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_payment_method"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/receipt_transaction_number_label"
                    android:layout_marginStart="@dimen/space_normal"
                    android:layout_marginTop="@dimen/space_normal"
                    android:layout_toEndOf="@+id/receipt_payment_method_label"
                    android:textSize="@dimen/space_normal"
                    tools:text="@string/credit_card_receipt"
                    android:layout_marginLeft="@dimen/space_normal"
                    android:layout_toRightOf="@+id/receipt_payment_method_label" />

                <LinearLayout
                    android:id="@+id/receipt_table_header"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/receipt_payment_method_label"
                    android:layout_marginTop="@dimen/space_big"
                    android:background="@color/vas_receipt_table_header"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.6"
                        android:text="Option"
                        android:textColor="@color/vas_main_text_color" />

                    <androidx.appcompat.widget.AppCompatTextView

                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.2"
                        android:text="Durée"
                        android:textColor="@color/vas_main_text_color" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.2"
                        android:text="Prix"
                        android:textColor="@color/vas_main_text_color" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/receipt_table_header_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/receipt_table_header"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/receipt_order_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.6"
                        android:textColor="@color/vas_main_text_color"
                        android:textStyle="bold"
                        tools:text="Renouvellement +Annonce Star" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/receipt_order_duration"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.2"
                        android:textColor="@color/vas_main_text_color"
                        android:textStyle="bold"
                        tools:text="4 jours" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/receipt_package_price"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/space_small"
                        android:layout_weight="0.2"
                        android:textColor="@color/vas_main_text_color"
                        android:textStyle="bold"
                        tools:text="1 222 DH" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/receipt_total"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_below="@+id/receipt_table_header_content"
                    android:layout_marginTop="@dimen/space_huge"
                    android:text="@string/vas_payment_total"
                    android:textColor="@color/vas_main_text_color"
                    android:textSize="@dimen/text_size_big"
                    android:textStyle="bold" />

            </RelativeLayout>


        </LinearLayout>
    </ScrollView>

    <include
        android:id="@+id/receipt_footer"
        layout="@layout/partial_receipt_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true" />
</RelativeLayout>
