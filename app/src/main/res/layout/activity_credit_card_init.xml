<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="se.scmv.morocco.avitov2.vas.presentation.activities.EmbeddedWebViewActivity">

    <!--Toolbar-->
    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <!--Web view-->
    <WebView
        android:id="@+id/payzone_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/toolbar" />

    <!--Progress indicator-->
    <ProgressBar
        android:id="@+id/progress_indicator"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_centerInParent="true" />

    <!--Error message-->
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:text="@string/vas_customer_token_error_message"
        android:textSize="24sp"
        android:textStyle="bold"
        android:visibility="gone" />

</RelativeLayout>
